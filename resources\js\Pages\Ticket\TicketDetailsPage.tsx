import { useState } from "react";
import { formatDistanceToNow } from "date-fns";
import {
  MessageCircle,
  Clock,
  Tag,
  ChevronUp,
  ArrowLeft,
  Send,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Card } from "@/components/ui/card";
import { Ticket } from "@/types/ticket";
import { useTickets } from "@/hooks/useTickets";
import { currentUser } from "@/lib/mockData";

interface TicketDetailsPageProps {
  ticket: Ticket;
  onBack: () => void;
}

export function TicketDetailsPage({ ticket, onBack }: TicketDetailsPageProps) {
  const { toggleUpvote } = useTickets();
  const hasUpvoted = ticket.upvotedBy.includes(currentUser.id);
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "low":
        return "bg-green-100 text-green-800 border-green-200";
      case "medium":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "high":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "urgent":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "open":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "in-progress":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "waiting-response":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "resolved":
        return "bg-green-100 text-green-800 border-green-200";
      case "closed":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const handleUpvote = () => {
    toggleUpvote(ticket.id);
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!comment.trim() || isSubmitting) return;

    setIsSubmitting(true);

    // Simulate API call
    setTimeout(() => {
      // Here you would normally call an API to add the comment
      console.log("Comment submitted:", comment);
      setComment("");
      setIsSubmitting(false);
      // You could also update the ticket replies here
    }, 1000);
  };

  return (
    <div className=" max-w-4xl space-y-6 justify-center items-center">
      {/* Back Button */}
      <Button variant="ghost" onClick={onBack} className="gap-2 mb-4">
        <ArrowLeft className="h-4 w-4" />
        Back to tickets
      </Button>

      {/* Content */}
      <Card className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1 min-w-0">
            {/* Upvote Section */}
            <div className="flex flex-col items-center gap-2 pt-1">
              <Button
                variant="ghost"
                size="sm"
                className={`h-10 w-10 p-0 transition-colors ${
                  hasUpvoted
                    ? "text-orange-600 bg-orange-50 hover:bg-orange-100"
                    : "text-muted-foreground hover:text-orange-600 hover:bg-orange-50"
                }`}
                onClick={handleUpvote}
              >
                <ChevronUp
                  className={`h-5 w-5 ${hasUpvoted ? "fill-current" : ""}`}
                />
              </Button>
              <div className="text-center">
                <div
                  className={`text-sm font-semibold ${
                    hasUpvoted ? "text-orange-600" : "text-muted-foreground"
                  }`}
                >
                  {ticket.upvotes}
                </div>
                <div className="text-xs text-muted-foreground">
                  {ticket.upvotes === 1 ? "vote" : "votes"}
                </div>
              </div>
            </div>

            {/* <span className="text-2xl">{getCategoryIcon(ticket.category)}</span> */}
            <div className="flex-1 min-w-0">
              <h1 className="text-2xl font-bold leading-tight mb-2">
                {ticket.title}
              </h1>
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <span>#{ticket.id}</span>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>
                    Created{" "}
                    {formatDistanceToNow(ticket.createdAt, { addSuffix: true })}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageCircle className="w-4 h-4" />
                  <span>{ticket.replies.length} replies</span>
                </div>
                <div className="flex items-center gap-1">
                  <ChevronUp className="w-4 h-4" />
                  <span>{ticket.upvotes} upvotes</span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex items-center gap-2 ml-4">
            <Badge className={`text-xs ${getPriorityColor(ticket.priority)}`}>
              {ticket.priority.charAt(0).toUpperCase() +
                ticket.priority.slice(1)}
            </Badge>
            <Badge className={`text-xs ${getStatusColor(ticket.status)}`}>
              {ticket.status
                .split("-")
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join(" ")}
            </Badge>
          </div>
        </div>
        <div className="space-y-6">
          {/* Description */}
          <div className="space-y-3">
            <h3 className="font-semibold">Description</h3>
            <div className="p-4 bg-muted/30 border rounded-lg">
              <p className="text-sm leading-relaxed whitespace-pre-wrap">
                {ticket.description}
              </p>
            </div>
          </div>

          {/* Ticket Info */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="space-y-1">
              <div className="text-sm font-medium">Category</div>
              <div className="text-sm text-muted-foreground capitalize">
                {ticket.category.replace("-", " ")}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-sm font-medium">Author</div>
              <div className="flex items-center gap-2">
                <Avatar className="w-5 h-5">
                  <AvatarImage src={ticket.author.avatar} />
                  <AvatarFallback className="text-xs">
                    {ticket.author.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm text-muted-foreground">
                  {ticket.author.name}
                </span>
                {ticket.author.isStaff && (
                  <Badge variant="outline" className="text-xs">
                    Staff
                  </Badge>
                )}
              </div>
            </div>
            {ticket.assignee && (
              <div className="space-y-1">
                <div className="text-sm font-medium">Assigned to</div>
                <div className="flex items-center gap-2">
                  <Avatar className="w-5 h-5">
                    <AvatarImage src={ticket.assignee.avatar} />
                    <AvatarFallback className="text-xs">
                      {ticket.assignee.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-muted-foreground">
                    {ticket.assignee.name}
                  </span>
                  {ticket.assignee.isStaff && (
                    <Badge variant="outline" className="text-xs">
                      Staff
                    </Badge>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Tags */}
          {ticket.tags.length > 0 && (
            <div className="space-y-3">
              <h3 className="font-semibold flex items-center gap-2">
                <Tag className="w-4 h-4" />
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {ticket.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Replies */}
          {ticket.replies.length > 0 && (
            <div className="space-y-4">
              <h3 className="font-semibold flex items-center gap-2">
                <MessageCircle className="w-4" />
                Replies ({ticket.replies.length})
              </h3>
              <div className="space-y-4">
                {ticket.replies.map((reply) => (
                  <div key={reply.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={reply.author.avatar} />
                          <AvatarFallback className="text-sm">
                            {reply.author.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-sm">
                              {reply.author.name}
                            </span>
                            {reply.author.isStaff && (
                              <Badge variant="outline" className="text-xs">
                                Staff
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground">
                            {formatDistanceToNow(reply.createdAt, {
                              addSuffix: true,
                            })}
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-sm leading-relaxed whitespace-pre-wrap">
                      {reply.content}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Comment Form */}
          <Separator />
          <div className="space-y-4">
            <h3 className="font-semibold flex items-center gap-2">
              <MessageCircle className="w-4 h-4" />
              Add a comment
            </h3>
            <form onSubmit={handleSubmitComment} className="space-y-4">
              <div className="flex items-start gap-3">
                <Avatar className="w-8 h-8 mt-1">
                  <AvatarImage src={currentUser.avatar} />
                  <AvatarFallback className="text-sm">
                    {currentUser.name
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 space-y-3">
                  <Textarea
                    placeholder="Write your comment here..."
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    className="min-h-[100px] resize-none"
                    disabled={isSubmitting}
                  />
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground">
                      Commenting as{" "}
                      <span className="font-medium">{currentUser.name}</span>
                    </div>
                    <Button
                      type="submit"
                      disabled={!comment.trim() || isSubmitting}
                      className="gap-2"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                          Posting...
                        </>
                      ) : (
                        <>
                          <Send className="w-4 h-4" />
                          Post Comment
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </Card>
    </div>
  );
}
