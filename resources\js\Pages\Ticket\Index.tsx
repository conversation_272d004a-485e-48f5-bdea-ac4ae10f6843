import { Help<PERSON>ir<PERSON>, Settings, LogOut } from 'lucide-react';
import { But<PERSON> } from '@/Components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/Components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/Components/ui/dropdown-menu';


import React from 'react';
import { FilterSidebar } from './FilterSidebar';
import { TicketList } from './TicketList';

export function App() {
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
     

      <div className="flex">
        {/* Sidebar */}
        <FilterSidebar />

        {/* Main Content */}
        <main className="flex-1 flex justify-center p-6">
          {/* <div className="w-full container">
           
          </div> */}

          <TicketList />
        </main>
      </div>
    </div>
  );
}

export default App;