import React from 'react';
import { router } from '@inertiajs/react';
import AppLayout from '@/Layouts/AppLayout';
import { Badge } from '@/Components/ui/badge';
import { Button } from '@/Components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/Components/ui/card';
import { Input } from '@/Components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/Components/ui/select';
import { Search, Plus, Filter } from 'lucide-react';
import { AvatarWithFallback } from '@/Components/ui/avatar-with-fallback';
import Pagination from '@/Components/Pagination';
import { formatDistanceToNow } from 'date-fns';
import { Avatar } from '@/Components/ui/avatar';
import { AvatarImage } from '@radix-ui/react-avatar';

interface TicketIndexProps {
  tickets: any[];
  categories: any[];
  tags: any[];
  departments: any[];
  users: any[];
  ticketCount: number;
  pagination: any;
  keyword: string;
  notifications: any[];
  sort: string;
  filters: {
    status?: string;
    priority?: string;
    department?: string;
    assignee?: string;
  };
}

const TicketIndex: React.FC<TicketIndexProps> = ({
  tickets = [],
  categories = [],
  tags = [],
  departments = [],
  users = [],
  ticketCount,
  pagination,
  keyword,
  notifications,
  sort,
  filters,
}) => {
  const title = 'Support Tickets';

  const handleSearch = (value: string) => {
    if (value.trim()) {
      router.get('/tickets/search', { search: value, page: 1 });
    } else {
      router.get('/tickets');
    }
  };

  const handleFilterChange = (filterType: string, value: string) => {
    const newFilters = { ...filters, [filterType]: value };
    router.get('/tickets', { ...newFilters, search: keyword });
  };

  const handleSortChange = (value: string) => {
    router.get('/tickets', { ...filters, search: keyword, sort: value });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'in_progress': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'closed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <AppLayout
      title={title}
      canLogin={true}
      canRegister={true}
      notifications={notifications}
    >
      <div className="max-w-[1450px] mx-auto lg:px-4 dark:bg-[#0F1014]">
        <div className="flex">
          {/* Main Content */}
          <div className="flex-1 w-full max-w-5xl mx-auto mt-4 sm:mt-5 md:mt-7 px-4 sm:px-6 md:px-4">
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Support Tickets
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                  {ticketCount} tickets found
                </p>
              </div>
              <Button
                onClick={() => router.get('/tickets/create')}
                className="flex items-center gap-2"
              >
                <Plus className="h-4 w-4" />
                New Ticket
              </Button>
            </div>

            {/* Search and Filters */}
            <Card className="mb-6">
              <CardContent className="p-4">
                <div className="flex flex-col lg:flex-row gap-4">
                  {/* Search */}
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                      <Input
                        placeholder="Search tickets..."
                        defaultValue={keyword}
                        className="pl-10"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleSearch(e.currentTarget.value);
                          }
                        }}
                      />
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="flex gap-2">
                    <Select value={filters.status || ''} onValueChange={(value) => handleFilterChange('status', value)}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Status</SelectItem>
                        <SelectItem value="open">Open</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="resolved">Resolved</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={filters.priority || ''} onValueChange={(value) => handleFilterChange('priority', value)}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Priority" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All Priority</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="low">Low</SelectItem>
                      </SelectContent>
                    </Select>

                    <Select value={sort} onValueChange={handleSortChange}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="latest">Latest</SelectItem>
                        <SelectItem value="oldest">Oldest</SelectItem>
                        <SelectItem value="priority">Priority</SelectItem>
                        <SelectItem value="upvotes">Most Voted</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tickets List */}
            <div className="space-y-4">
              {tickets.map((ticket) => (
                <Card
                  key={ticket.id}
                  className="hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => router.get(`/tickets/${ticket.slug}`)}
                >
                  <CardContent className="p-6">
                    <div className="flex justify-between items-start mb-3">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                          {ticket.title}
                        </h3>
                        <div className="flex items-center gap-3 mb-3">
                          <Badge className={getPriorityColor(ticket.priority)}>
                            {ticket.priority_name || ticket.priority}
                          </Badge>
                          <Badge className={getStatusColor(ticket.status)}>
                            {ticket.status_name || ticket.status}
                          </Badge>
                          {ticket.department && (
                            <Badge variant="outline">
                              {ticket.department.name}
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {/* {formatDistanceToNow(new Date(ticket.created_at), { addSuffix: true })} */}

                          {ticket.created_at}
                        </p>
                      </div>
                    </div>

                    <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">
                      {ticket.content}
                    </p>

                    <div className="flex justify-between items-center">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          <Avatar>
                            <AvatarImage src={ticket.user?.profile_photo_path}></AvatarImage>
                          </Avatar>
                          <span className="text-sm text-gray-600 dark:text-gray-400">
                            {ticket.user?.name}
                          </span>
                        </div>
                        {ticket.assignee && (
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-gray-500">→</span>
                            <AvatarWithFallback
                              src={ticket.assignee?.profile_photo_path}
                              alt={ticket.assignee?.name}
                              fallback={ticket.assignee?.name}
                              className="h-6 w-6"
                            />
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {ticket.assignee?.name}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span>{ticket.comments_count || 0} comments</span>
                        <span>{ticket.upvote_count || 0} votes</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Pagination */}
            {pagination && pagination.total > 0 && (
              <div className="mt-8 flex justify-center">
                <Pagination
                  current_page={pagination.current_page}
                  next_page_url={pagination.next_page_url}
                  prev_page_url={pagination.prev_page_url}
                  last_page={pagination.last_page}
                />
              </div>
            )}

            {/* Empty State */}
            {tickets.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  No tickets found matching your criteria.
                </p>
                <Button onClick={() => router.get('/tickets/create')}>
                  Create your first ticket
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default TicketIndex;