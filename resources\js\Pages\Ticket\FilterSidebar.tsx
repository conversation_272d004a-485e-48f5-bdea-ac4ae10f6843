import { Search, Filter, User, Users } from "lucide-react";
import { Input } from "@/Components/ui/input";
import { Button } from "@/Components/ui/button";
import { Badge } from "@/Components/ui/badge";
import { Separator } from "@/Components/ui/separator";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/Components/ui/select";


import React from "react";





const sortOptions = [
  { value: "newest", label: "Newest First" },
  { value: "oldest", label: "Oldest First" },
  { value: "most-upvoted", label: "Most Upvoted" },
  { value: "most-replies", label: "Most Replies" },
];

export function FilterSidebar() {
 

  const clearFilters = () => {
    updateFilters({
      category: "",
      priority: "",
      status: "",
      search: "",
      myTickets: "",
      sortBy: "",
    });
  };

  const hasActiveFilters = Object.values(filters).some(
    (value) => value !== undefined && value !== ""
  );

  return (
    <div className="w-80 bg-card p-6 border-r">
      <div className="space-y-6">
        {/* Header */}
        {/* <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </h2>
          {hasActiveFilters && (
            <Button variant={'ghost'} size="sm" onClick={clearFilters} className='text-white'>
              Clear all
            </Button>
          )}
        </div> */}

        {/* Search */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Search</label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tickets..."
              value={filters.search || ""}
              onChange={(e) => updateFilters({ search: e.target.value })}
              className="pl-10"
            />
          </div>
        </div>

        <Separator />

        {/* View Toggle */}
        <div className="space-y-3">
          <label className="text-sm font-medium text-foreground">View</label>
          <div className="bg-muted/30 p-1 rounded-lg space-y-1">
            <button
              onClick={() => updateFilters({ myTickets: false })}
              className={`w-full flex items-center gap-3 px-4 py-3 rounded-md text-left transition-all duration-200 font-medium ${
                !filters.myTickets
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
              }`}
            >
              <Users className="h-4 w-4 shrink-0" />
              <span>All Tickets</span>
            </button>
            <button
              onClick={() => updateFilters({ myTickets: true })}
              className={`w-full flex items-center gap-3 px-4 py-3 rounded-md text-left transition-all duration-200 font-medium ${
                filters.myTickets
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted/50 text-muted-foreground hover:text-foreground"
              }`}
            >
              <User className="h-4 w-4 shrink-0" />
              <span>My Tickets</span>
            </button>
          </div>
        </div>

        <Separator />

        {/* Category */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Category</label>
          <Select
            value={filters.category || ""}
            onValueChange={(value) =>
              updateFilters({
                category:
                  value === "all" ? undefined : (value as TicketCategory),
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.value} value={category.value}>
                  {category.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Priority */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Priority</label>
          <Select
            value={filters.priority || ""}
            onValueChange={(value) =>
              updateFilters({
                priority:
                  value === "all" ? undefined : (value as TicketPriority),
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              {priorities.map((priority) => (
                <SelectItem key={priority.value} value={priority.value}>
                  <div className="flex items-center gap-2">
                    <Badge className={`text-xs ${priority.color}`}>
                      {priority.label}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Status</label>
          <Select
            value={filters.status || ""}
            onValueChange={(value) =>
              updateFilters({
                status: value === "all" ? undefined : (value as TicketStatus),
              })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              {statuses.map((status) => (
                <SelectItem key={status.value} value={status.value}>
                  <div className="flex items-center gap-2">
                    <Badge className={`text-xs ${status.color}`}>
                      {status.label}
                    </Badge>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Sort By */}
        <div className="space-y-3">
          <label className="text-sm font-medium">Sort By</label>
          <Select
            value={filters.sortBy || "newest"}
            onValueChange={(value) =>
              updateFilters({ sortBy: value === "newest" ? undefined : value })
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Active Filters */}
        {hasActiveFilters && (
          <>
            <Separator />
            <div className="space-y-3">
              <label className="text-sm font-medium">Active Filters</label>
              <div className="flex flex-wrap gap-2">
                {filters.myTickets && (
                  <Badge variant="secondary" className="text-xs">
                    My Tickets
                  </Badge>
                )}
                {filters.category && (
                  <Badge variant="secondary" className="text-xs">
                    {
                      categories.find((c) => c.value === filters.category)
                        ?.label
                    }
                  </Badge>
                )}
                {filters.priority && (
                  <Badge variant="secondary" className="text-xs">
                    {
                      priorities.find((p) => p.value === filters.priority)
                        ?.label
                    }
                  </Badge>
                )}
                {filters.status && (
                  <Badge variant="secondary" className="text-xs">
                    {statuses.find((s) => s.value === filters.status)?.label}
                  </Badge>
                )}
                {filters.search && (
                  <Badge variant="secondary" className="text-xs">
                    Search: {filters.search}
                  </Badge>
                )}
                {filters.sortBy && filters.sortBy !== "newest" && (
                  <Badge variant="secondary" className="text-xs">
                    Sort:{" "}
                    {sortOptions.find((s) => s.value === filters.sortBy)?.label}
                  </Badge>
                )}
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
}


