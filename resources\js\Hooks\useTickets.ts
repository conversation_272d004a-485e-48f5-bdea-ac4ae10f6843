import { useState, useMemo } from "react";
import { Ticket, TicketFilters, Notification } from "@/types/ticket";
import { mockTickets, mockNotifications, currentUser } from "@/lib/mockData";

const TICKETS_PER_PAGE = 5;

export function useTickets() {
  const [tickets, setTickets] = useState<Ticket[]>(mockTickets);
  const [notifications, setNotifications] =
    useState<Notification[]>(mockNotifications);
  const [filters, setFilters] = useState<TicketFilters>({});
  const [currentPage, setCurrentPage] = useState(1);

  const filteredTickets = useMemo(() => {
    let filtered = tickets.filter((ticket) => {
      if (filters.myTickets && ticket.author.id !== currentUser.id) {
        return false;
      }

      if (filters.category && ticket.category !== filters.category) {
        return false;
      }

      if (filters.priority && ticket.priority !== filters.priority) {
        return false;
      }

      if (filters.status && ticket.status !== filters.status) {
        return false;
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          ticket.title.toLowerCase().includes(searchLower) ||
          ticket.description.toLowerCase().includes(searchLower) ||
          ticket.tags.some((tag) => tag.toLowerCase().includes(searchLower))
        );
      }

      return true;
    });

    // Apply sorting
    const sortBy = filters.sortBy || "newest";
    switch (sortBy) {
      case "oldest":
        return filtered.sort(
          (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
        );
      case "most-upvoted":
        return filtered.sort((a, b) => b.upvotes - a.upvotes);
      case "most-replies":
        return filtered.sort((a, b) => b.replies.length - a.replies.length);
      case "newest":
      default:
        return filtered.sort(
          (a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()
        );
    }
  }, [tickets, filters]);

  const totalPages = Math.ceil(filteredTickets.length / TICKETS_PER_PAGE);
  const startIndex = (currentPage - 1) * TICKETS_PER_PAGE;
  const paginatedTickets = filteredTickets.slice(
    startIndex,
    startIndex + TICKETS_PER_PAGE
  );

  const unreadNotifications = useMemo(() => {
    return notifications.filter((notif) => !notif.read);
  }, [notifications]);

  const updateFilters = (newFilters: Partial<TicketFilters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change
  };

  const createTicket = (
    ticketData: Omit<Ticket, "id" | "createdAt" | "updatedAt" | "replies">
  ) => {
    const newTicket: Ticket = {
      ...ticketData,
      id: `ticket-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date(),
      replies: [],
      upvotes: 0,
      upvotedBy: [],
    };

    setTickets((prev) => [newTicket, ...prev]);
    return newTicket;
  };

  const toggleUpvote = (ticketId: string) => {
    setTickets((prev) =>
      prev.map((ticket) => {
        if (ticket.id === ticketId) {
          const hasUpvoted = ticket.upvotedBy.includes(currentUser.id);

          if (hasUpvoted) {
            // Remove upvote
            return {
              ...ticket,
              upvotes: ticket.upvotes - 1,
              upvotedBy: ticket.upvotedBy.filter((id) => id !== currentUser.id),
              updatedAt: new Date(),
            };
          } else {
            // Add upvote
            return {
              ...ticket,
              upvotes: ticket.upvotes + 1,
              upvotedBy: [...ticket.upvotedBy, currentUser.id],
              updatedAt: new Date(),
            };
          }
        }
        return ticket;
      })
    );
  };
  const markNotificationAsRead = (notificationId: string) => {
    setNotifications((prev) =>
      prev.map((notif) =>
        notif.id === notificationId ? { ...notif, read: true } : notif
      )
    );
  };

  const markAllNotificationsAsRead = () => {
    setNotifications((prev) => prev.map((notif) => ({ ...notif, read: true })));
  };

  const goToPage = (page: number) => {
    setCurrentPage(Math.max(1, Math.min(page, totalPages)));
  };

  return {
    tickets: paginatedTickets,
    totalTickets: filteredTickets.length,
    currentPage,
    totalPages,
    ticketsPerPage: TICKETS_PER_PAGE,
    notifications,
    unreadNotifications,
    filters,
    updateFilters,
    createTicket,
    toggleUpvote,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    goToPage,
  };
}
