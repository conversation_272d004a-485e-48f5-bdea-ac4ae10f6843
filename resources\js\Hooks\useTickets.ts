import { useState, useMemo } from "react";
import { router, usePage } from "@inertiajs/react";

// Updated interfaces to match our new structure
interface Author {
  id?: string | number;
  name: string;
  avatar?: string;
  isStaff?: boolean;
}

interface Assignee {
  name: string;
  avatar?: string;
}

interface Ticket {
  id: string | number;
  title: string;
  description: string;
  priority?: string;
  status?: string;
  tags?: string[];
  upvotes: number;
  upvotedBy?: any[];
  updatedAt?: Date;
  createdAt?: Date;
  author: Author;
  replies?: any[];
  assignee?: Assignee | null;
}

interface TicketFilters {
  search?: string;
  status?: string;
  priority?: string;
  category?: string;
  department?: string;
  assignee?: string;
  myTickets?: boolean;
  sortBy?: string;
}

const TICKETS_PER_PAGE = 5;

export function useTickets(initialTickets?: any[], initialFilters?: TicketFilters) {
  const { props } = usePage();
  const [filters, setFilters] = useState<TicketFilters>(initialFilters || {});
  const [currentPage, setCurrentPage] = useState(1);

  // Get tickets from props or use initial tickets
  const tickets: Ticket[] = useMemo(() => {
    if (initialTickets) {
      return initialTickets.map((ticket: any) => ({
        id: ticket.id,
        title: ticket.title || 'Untitled',
        description: ticket.content || '',
        priority: ticket.priority || 'medium',
        status: ticket.status ? ticket.status.replace('_', '-') : 'open',
        tags: ticket.tags ? ticket.tags.map((tag: any) => tag.name) : [],
        upvotes: ticket.upvote_count || 0,
        upvotedBy: [], // This would need to be populated from backend
        updatedAt: new Date(ticket.updated_at || ticket.created_at || Date.now()),
        createdAt: new Date(ticket.created_at || Date.now()),
        author: {
          id: ticket.user?.id,
          name: ticket.user?.name || 'Unknown',
          avatar: ticket.user?.profile_photo_path,
          isStaff: ticket.user?.is_admin || false
        },
        replies: ticket.comments || [],
        assignee: ticket.assignee ? {
          name: ticket.assignee.name,
          avatar: ticket.assignee.profile_photo_path
        } : null
      }));
    }
    return [];
  }, [initialTickets]);

  const currentUser = (props as any)?.auth?.user;

  const filteredTickets = useMemo(() => {
    let filtered = tickets.filter((ticket: Ticket) => {
      if (filters.myTickets && currentUser && ticket.author.id !== currentUser.id) {
        return false;
      }

      if (filters.priority && ticket.priority !== filters.priority) {
        return false;
      }

      if (filters.status && ticket.status !== filters.status) {
        return false;
      }

      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        return (
          ticket.title.toLowerCase().includes(searchLower) ||
          ticket.description.toLowerCase().includes(searchLower) ||
          (ticket.tags && ticket.tags.some((tag: string) => tag.toLowerCase().includes(searchLower)))
        );
      }

      return true;
    });

    // Apply sorting
    const sortBy = filters.sortBy || "newest";
    switch (sortBy) {
      case "oldest":
        return filtered.sort(
          (a: Ticket, b: Ticket) => (a.createdAt?.getTime() || 0) - (b.createdAt?.getTime() || 0)
        );
      case "most-upvoted":
        return filtered.sort((a: Ticket, b: Ticket) => b.upvotes - a.upvotes);
      case "most-replies":
        return filtered.sort((a: Ticket, b: Ticket) => (b.replies?.length || 0) - (a.replies?.length || 0));
      case "newest":
      default:
        return filtered.sort(
          (a: Ticket, b: Ticket) => (b.updatedAt?.getTime() || 0) - (a.updatedAt?.getTime() || 0)
        );
    }
  }, [tickets, filters, currentUser]);

  const totalPages = Math.ceil(filteredTickets.length / TICKETS_PER_PAGE);
  const startIndex = (currentPage - 1) * TICKETS_PER_PAGE;
  const paginatedTickets = filteredTickets.slice(
    startIndex,
    startIndex + TICKETS_PER_PAGE
  );

  // Get notifications from props
  const notifications = (props as any)?.notifications || [];
  const unreadNotifications = useMemo(() => {
    return notifications.filter((notif: any) => !notif.read);
  }, [notifications]);

  const updateFilters = (newFilters: Partial<TicketFilters>) => {
    setFilters((prev) => ({ ...prev, ...newFilters }));
    setCurrentPage(1); // Reset to first page when filters change

    // Navigate with new filters using Inertia
    const searchParams = new URLSearchParams();
    const updatedFilters = { ...filters, ...newFilters };

    Object.entries(updatedFilters).forEach(([key, value]) => {
      if (value && value !== '') {
        searchParams.set(key, value.toString());
      }
    });

    router.get(`/tickets?${searchParams.toString()}`, {}, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const createTicket = (ticketData: any) => {
    // Use Inertia to navigate to create page or submit form
    router.post('/tickets', ticketData);
  };

  const toggleUpvote = (ticketId: string | number) => {
    // Make API call to toggle upvote
    router.post(`/tickets/${ticketId}/upvote`, {}, {
      preserveState: true,
      preserveScroll: true,
      onSuccess: () => {
        // Optionally refresh the page data
        router.reload({ only: ['tickets'] });
      }
    });
  };

  const markNotificationAsRead = (notificationId: string) => {
    router.post(`/notifications/${notificationId}/read`, {}, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const markAllNotificationsAsRead = () => {
    router.post('/notifications/mark-all-read', {}, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const goToPage = (page: number) => {
    const newPage = Math.max(1, Math.min(page, totalPages));
    setCurrentPage(newPage);

    // Navigate with pagination using Inertia
    const searchParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value && value !== '') {
        searchParams.set(key, value.toString());
      }
    });
    searchParams.set('page', newPage.toString());

    router.get(`/tickets?${searchParams.toString()}`, {}, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  return {
    tickets: paginatedTickets,
    totalTickets: filteredTickets.length,
    currentPage,
    totalPages,
    ticketsPerPage: TICKETS_PER_PAGE,
    notifications,
    unreadNotifications,
    filters,
    updateFilters,
    createTicket,
    toggleUpvote,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    goToPage,
  };
}
